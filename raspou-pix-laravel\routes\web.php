<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\GameController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\TransactionController;
use Illuminate\Support\Facades\Route;

// Public routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/games', [GameController::class, 'index'])->name('games.index');
Route::get('/games/{game:slug}', [GameController::class, 'show'])->name('games.show');

// Authenticated routes
Route::get('/dashboard', [DashboardController::class, 'index'])->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    // Profile routes
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Game routes
    Route::post('/games/{game}/play', [GameController::class, 'play'])->name('games.play');
    Route::post('/games/{gamePlay}/scratch', [GameController::class, 'scratch'])->name('games.scratch');

    // Transaction routes
    Route::get('/transactions', [TransactionController::class, 'index'])->name('transactions.index');
    Route::post('/deposit', [TransactionController::class, 'deposit'])->name('transactions.deposit');
    Route::post('/withdraw', [TransactionController::class, 'withdraw'])->name('transactions.withdraw');

    // Wallet routes
    Route::get('/wallet', [DashboardController::class, 'wallet'])->name('wallet');
});

require __DIR__.'/auth.php';
