<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            [
                'key' => 'site_name',
                'value' => 'Raspou Pix',
                'type' => 'string',
                'description' => 'Nome do site',
                'group' => 'general',
                'is_public' => true,
            ],
            [
                'key' => 'site_description',
                'value' => 'A maior plataforma de raspadinhas online do Brasil',
                'type' => 'string',
                'description' => 'Descrição do site',
                'group' => 'general',
                'is_public' => true,
            ],
            [
                'key' => 'maintenance_mode',
                'value' => 'false',
                'type' => 'boolean',
                'description' => 'Modo de manutenção',
                'group' => 'system',
                'is_public' => true,
            ],
            [
                'key' => 'min_deposit',
                'value' => '10.00',
                'type' => 'float',
                'description' => 'Valor mínimo de depósito',
                'group' => 'payment',
                'is_public' => true,
            ],
            [
                'key' => 'min_withdrawal',
                'value' => '20.00',
                'type' => 'float',
                'description' => 'Valor mínimo de saque',
                'group' => 'payment',
                'is_public' => true,
            ],
            [
                'key' => 'affiliate_commission',
                'value' => '5.00',
                'type' => 'float',
                'description' => 'Comissão de afiliado (%)',
                'group' => 'affiliate',
                'is_public' => false,
            ],
        ];

        foreach ($settings as $setting) {
            \App\Models\Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
