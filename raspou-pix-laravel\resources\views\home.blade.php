<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ $settings['site_name']->value ?? 'Ra<PERSON>ou Pix' }} - Raspe e Ganhe Prêmios Instantâneos</title>
    <meta name="description" content="Plataforma de jogos de raspadinha online. Jogue e ganhe prêmios!">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <style>
        .scratch-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .scratch-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.4);
        }
        .prize-glow {
            animation: glow 2s ease-in-out infinite alternate;
        }
        @keyframes glow {
            from { box-shadow: 0 0 20px #ffd700; }
            to { box-shadow: 0 0 30px #ffd700, 0 0 40px #ffd700; }
        }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <!-- Header -->
    <header class="bg-gray-800 shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-yellow-400">🎰 Raspou Pix</h1>
                </div>
                <nav class="hidden md:flex space-x-8">
                    <a href="{{ route('home') }}" class="text-white hover:text-yellow-400 transition">Início</a>
                    <a href="{{ route('games.index') }}" class="text-white hover:text-yellow-400 transition">Jogos</a>
                    @auth
                        <a href="{{ route('dashboard') }}" class="text-white hover:text-yellow-400 transition">Dashboard</a>
                        <a href="{{ route('wallet') }}" class="text-white hover:text-yellow-400 transition">Carteira</a>
                    @endauth
                </nav>
                <div class="flex items-center space-x-4">
                    @auth
                        <span class="text-yellow-400 font-semibold">R$ {{ number_format(auth()->user()->balance, 2, ',', '.') }}</span>
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg transition">
                                Sair
                            </button>
                        </form>
                    @else
                        <a href="{{ route('login') }}" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition">
                            Entrar
                        </a>
                        <a href="{{ route('register') }}" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg transition">
                            Cadastrar
                        </a>
                    @endauth
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-purple-900 via-blue-900 to-indigo-900 py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-5xl font-bold mb-6">
                Raspe e Ganhe <span class="text-yellow-400">Prêmios Instantâneos!</span>
            </h2>
            <p class="text-xl mb-8 text-gray-300">
                A maior plataforma de raspadinhas online do Brasil. Compre no PIX e receba no PIX!
            </p>
            @guest
                <a href="{{ route('register') }}" class="bg-yellow-500 hover:bg-yellow-600 text-black font-bold py-4 px-8 rounded-full text-lg transition transform hover:scale-105">
                    Começar a Jogar Agora
                </a>
            @endguest
        </div>
    </section>

    <!-- Featured Games -->
    @if($featuredGames->count() > 0)
    <section class="py-16 bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h3 class="text-3xl font-bold text-center mb-12">🌟 Jogos em Destaque</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($featuredGames as $game)
                <div class="scratch-card p-6 text-center">
                    @if($game->image)
                        <img src="{{ $game->image }}" alt="{{ $game->name }}" class="w-full h-48 object-cover rounded-lg mb-4">
                    @endif
                    <h4 class="text-xl font-bold mb-2">{{ $game->name }}</h4>
                    <p class="text-gray-300 mb-4">{{ Str::limit($game->description, 100) }}</p>
                    <div class="flex justify-between items-center mb-4">
                        <span class="text-yellow-400 font-bold text-lg">R$ {{ number_format($game->price, 2, ',', '.') }}</span>
                        <span class="text-sm text-gray-400">{{ $game->remaining_cards }} restantes</span>
                    </div>
                    <a href="{{ route('games.show', $game->slug) }}" class="w-full bg-yellow-500 hover:bg-yellow-600 text-black font-bold py-3 px-6 rounded-lg transition transform hover:scale-105 inline-block">
                        Jogar Agora
                    </a>
                </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- Recent Winners -->
    @if($recentWinners->count() > 0)
    <section class="py-16 bg-gray-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h3 class="text-3xl font-bold text-center mb-12">🏆 Ganhadores Recentes</h3>
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="space-y-4">
                    @foreach($recentWinners as $winner)
                    <div class="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                        <div class="flex items-center space-x-4">
                            <div class="w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center">
                                <span class="font-bold text-black">{{ substr($winner->user->name, 0, 1) }}</span>
                            </div>
                            <div>
                                <p class="font-semibold">{{ $winner->user->name }}</p>
                                <p class="text-sm text-gray-400">{{ $winner->game->name }}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-yellow-400 font-bold">R$ {{ number_format($winner->prize_value, 2, ',', '.') }}</p>
                            <p class="text-xs text-gray-400">{{ $winner->completed_at->diffForHumans() }}</p>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </section>
    @endif

    <!-- All Games -->
    <section class="py-16 bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h3 class="text-3xl font-bold text-center mb-12">🎮 Todos os Jogos</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                @foreach($allGames as $game)
                <div class="bg-gray-700 rounded-lg p-4 hover:bg-gray-600 transition">
                    @if($game->image)
                        <img src="{{ $game->image }}" alt="{{ $game->name }}" class="w-full h-32 object-cover rounded-lg mb-3">
                    @endif
                    <h4 class="font-bold mb-2">{{ $game->name }}</h4>
                    <div class="flex justify-between items-center mb-3">
                        <span class="text-yellow-400 font-bold">R$ {{ number_format($game->price, 2, ',', '.') }}</span>
                        <span class="text-xs text-gray-400">{{ $game->remaining_cards }} restantes</span>
                    </div>
                    <a href="{{ route('games.show', $game->slug) }}" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded transition text-center inline-block">
                        Ver Jogo
                    </a>
                </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            <div class="mt-8">
                {{ $allGames->links() }}
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h5 class="text-lg font-bold mb-4 text-yellow-400">Raspou Pix</h5>
                    <p class="text-gray-400">A maior plataforma de raspadinhas online do Brasil.</p>
                </div>
                <div>
                    <h5 class="text-lg font-bold mb-4">Links Úteis</h5>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition">Como Jogar</a></li>
                        <li><a href="#" class="hover:text-white transition">Termos de Uso</a></li>
                        <li><a href="#" class="hover:text-white transition">Política de Privacidade</a></li>
                    </ul>
                </div>
                <div>
                    <h5 class="text-lg font-bold mb-4">Suporte</h5>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition">FAQ</a></li>
                        <li><a href="#" class="hover:text-white transition">Contato</a></li>
                        <li><a href="#" class="hover:text-white transition">WhatsApp</a></li>
                    </ul>
                </div>
                <div>
                    <h5 class="text-lg font-bold mb-4">Redes Sociais</h5>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition">Instagram</a></li>
                        <li><a href="#" class="hover:text-white transition">Facebook</a></li>
                        <li><a href="#" class="hover:text-white transition">Twitter</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2025 Raspou Pix. Todos os direitos reservados.</p>
            </div>
        </div>
    </footer>
</body>
</html>
