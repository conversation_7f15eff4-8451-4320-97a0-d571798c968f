<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class GamesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $games = [
            [
                'name' => '<PERSON><PERSON>ou Pix da Esperança',
                'slug' => 'raspou-pix-da-esperanca',
                'description' => 'O jogo mais popular! Raspe e ganhe até R$ 10.000,00 instantaneamente!',
                'price' => 5.00,
                'total_cards' => 1000,
                'remaining_cards' => 1000,
                'total_prize_value' => 3500.00,
                'remaining_prize_value' => 3500.00,
                'prize_distribution' => [
                    'grand_prize' => ['value' => 1000.00, 'quantity' => 1],
                    'major_prizes' => ['value' => 100.00, 'quantity' => 5],
                    'medium_prizes' => ['value' => 50.00, 'quantity' => 10],
                    'small_prizes' => ['value' => 10.00, 'quantity' => 50],
                ],
                'is_active' => true,
                'is_featured' => true,
                'house_edge' => 30.00,
            ],
            [
                'name' => 'Mega Sorte',
                'slug' => 'mega-sorte',
                'description' => 'Sua chance de ganhar o grande prêmio! Até R$ 5.000,00 te esperam!',
                'price' => 10.00,
                'total_cards' => 500,
                'remaining_cards' => 500,
                'total_prize_value' => 3000.00,
                'remaining_prize_value' => 3000.00,
                'prize_distribution' => [
                    'grand_prize' => ['value' => 500.00, 'quantity' => 2],
                    'major_prizes' => ['value' => 200.00, 'quantity' => 3],
                    'medium_prizes' => ['value' => 100.00, 'quantity' => 8],
                    'small_prizes' => ['value' => 20.00, 'quantity' => 30],
                ],
                'is_active' => true,
                'is_featured' => true,
                'house_edge' => 40.00,
            ],
            [
                'name' => 'Raspadinha Dourada',
                'slug' => 'raspadinha-dourada',
                'description' => 'Para quem quer apostar alto! Prêmios de até R$ 20.000,00!',
                'price' => 25.00,
                'total_cards' => 200,
                'remaining_cards' => 200,
                'total_prize_value' => 3000.00,
                'remaining_prize_value' => 3000.00,
                'prize_distribution' => [
                    'grand_prize' => ['value' => 2000.00, 'quantity' => 1],
                    'major_prizes' => ['value' => 500.00, 'quantity' => 2],
                    'medium_prizes' => ['value' => 250.00, 'quantity' => 4],
                    'small_prizes' => ['value' => 50.00, 'quantity' => 10],
                ],
                'is_active' => true,
                'is_featured' => true,
                'house_edge' => 40.00,
            ],
        ];

        foreach ($games as $gameData) {
            $game = \App\Models\Game::create($gameData);

            // Create prizes for each game
            $this->createPrizesForGame($game);
        }
    }

    private function createPrizesForGame($game)
    {
        $distribution = $game->prize_distribution;

        // Grand prize
        if (isset($distribution['grand_prize'])) {
            \App\Models\Prize::create([
                'game_id' => $game->id,
                'name' => 'Grande Prêmio',
                'description' => 'O maior prêmio do jogo!',
                'value' => $distribution['grand_prize']['value'],
                'type' => 'money',
                'quantity' => $distribution['grand_prize']['quantity'],
                'remaining_quantity' => $distribution['grand_prize']['quantity'],
                'probability' => $distribution['grand_prize']['quantity'] / $game->total_cards,
                'is_active' => true,
            ]);
        }

        // Major prizes
        if (isset($distribution['major_prizes'])) {
            \App\Models\Prize::create([
                'game_id' => $game->id,
                'name' => 'Prêmio Maior',
                'description' => 'Um excelente prêmio!',
                'value' => $distribution['major_prizes']['value'],
                'type' => 'money',
                'quantity' => $distribution['major_prizes']['quantity'],
                'remaining_quantity' => $distribution['major_prizes']['quantity'],
                'probability' => $distribution['major_prizes']['quantity'] / $game->total_cards,
                'is_active' => true,
            ]);
        }

        // Medium prizes
        if (isset($distribution['medium_prizes'])) {
            \App\Models\Prize::create([
                'game_id' => $game->id,
                'name' => 'Prêmio Médio',
                'description' => 'Um bom prêmio!',
                'value' => $distribution['medium_prizes']['value'],
                'type' => 'money',
                'quantity' => $distribution['medium_prizes']['quantity'],
                'remaining_quantity' => $distribution['medium_prizes']['quantity'],
                'probability' => $distribution['medium_prizes']['quantity'] / $game->total_cards,
                'is_active' => true,
            ]);
        }

        // Small prizes
        if (isset($distribution['small_prizes'])) {
            \App\Models\Prize::create([
                'game_id' => $game->id,
                'name' => 'Prêmio Pequeno',
                'description' => 'Melhor que nada!',
                'value' => $distribution['small_prizes']['value'],
                'type' => 'money',
                'quantity' => $distribution['small_prizes']['quantity'],
                'remaining_quantity' => $distribution['small_prizes']['quantity'],
                'probability' => $distribution['small_prizes']['quantity'] / $game->total_cards,
                'is_active' => true,
            ]);
        }
    }
}
