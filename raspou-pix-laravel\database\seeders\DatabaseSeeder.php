<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            SettingsSeeder::class,
            GamesSeeder::class,
        ]);

        // Create admin user
        User::factory()->create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'is_admin' => true,
            'balance' => 1000.00,
            'affiliate_code' => 'ADMIN001',
        ]);

        // Create test user
        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'balance' => 100.00,
            'affiliate_code' => 'TEST001',
        ]);
    }
}
