<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Game;
use App\Models\GamePlay;
use App\Models\Setting;

class HomeController extends Controller
{
    public function index()
    {
        $featuredGames = Game::featured()->available()->take(6)->get();
        $allGames = Game::available()->paginate(12);
        $recentWinners = GamePlay::winners()
            ->with(['user', 'game', 'prize'])
            ->recent(7)
            ->take(10)
            ->get();

        $settings = Setting::public()->get()->keyBy('key');

        return view('home', compact('featuredGames', 'allGames', 'recentWinners', 'settings'));
    }
}
