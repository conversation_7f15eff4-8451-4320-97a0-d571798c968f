@media (min-width:1024px){#scratch-card:hover{cursor:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 24 24'%3E%3Ccircle cx='12' cy='12' r='10' fill='rgba(255,255,255,0.2)' stroke='%23fff' stroke-width='2'/%3E%3C/svg%3E") 16 16,auto}#scratch-card:active{cursor:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 24 24'%3E%3Ccircle cx='12' cy='12' r='8' fill='rgba(255,255,255,0.3)' stroke='%23fff' stroke-width='2'/%3E%3C/svg%3E") 16 16,grabbing}.card{perspective:1000px;transform-style:preserve-3d}.card:hover .card-content{transform:translateZ(10px)}.btn-glow{overflow:hidden;position:relative}.btn-glow:after{background:radial-gradient(circle,hsla(0,0%,100%,.3) 0,hsla(0,0%,100%,0) 60%);content:"";height:200%;left:-50%;opacity:0;position:absolute;top:-50%;transform:scale(.5);transition:opacity .5s,transform .5s;width:200%}.btn-glow:hover:after{opacity:1;transform:scale(1)}.card.popular{position:relative}.card.popular:before{background:linear-gradient(135deg,#ff5e62,#f96);border-radius:0 0 10px 0;box-shadow:2px 2px 5px rgba(0,0,0,.2);color:#fff;content:"Popular";font-size:12px;font-weight:700;left:0;padding:5px 10px;position:absolute;top:0;z-index:10}@keyframes confetti-rain{0%{opacity:1;transform:translateY(-100px) rotate(0deg)}to{opacity:0;transform:translateY(1000px) rotate(2turn)}}.confetti-piece{animation:confetti-rain 5s ease-in-out infinite;background-color:red;height:20px;position:absolute;top:-100px;width:10px}@keyframes pulse-attention{0%{box-shadow:0 0 0 0 hsla(0,100%,71%,.7)}70%{box-shadow:0 0 0 10px hsla(0,100%,71%,0)}to{box-shadow:0 0 0 0 hsla(0,100%,71%,0)}}.pulse-attention{animation:pulse-attention 2s infinite}.glassmorphism{backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px);background:hsla(0,0%,100%,.1);border:1px solid hsla(0,0%,100%,.18);border-radius:10px;box-shadow:0 8px 32px 0 rgba(31,38,135,.37)}.interactive-hover{transition:all .3s cubic-bezier(.175,.885,.32,1.275)}.interactive-hover:hover{box-shadow:0 10px 20px rgba(0,0,0,.2);transform:translateY(-5px) scale(1.02)}@keyframes vibrate-gentle{0%{transform:translate(0)}20%{transform:translate(-2px,2px)}40%{transform:translate(-2px,-2px)}60%{transform:translate(2px,2px)}80%{transform:translate(2px,-2px)}to{transform:translate(0)}}.vibrate-attention{animation:vibrate-gentle .5s linear infinite;animation-play-state:paused}.vibrate-attention:hover{animation-play-state:running}}#back-to-top{opacity:0;transition:visibility 0s,opacity .5s linear;visibility:hidden}#back-to-top.visible{opacity:1;visibility:visible}